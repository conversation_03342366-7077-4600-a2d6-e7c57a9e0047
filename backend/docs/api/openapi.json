{"openapi": "3.0.3", "info": {"title": "GameFlex API", "version": "0.0.2", "description": "Comprehensive API documentation for the GameFlex platform", "contact": {"name": "GameFlex API Team", "email": "<EMAIL>"}}, "servers": [{"url": "https://api.gameflex.com/v1", "description": "GameFlex API Server"}], "paths": {"/auth/confirm-signup": {"post": {"summary": "Create confirm-signup", "description": "Confirm user signup with verification code", "tags": ["Authentication"], "operationId": "post_auth_confirm-signup", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/auth/forgot-password": {"post": {"summary": "Create forgot-password", "description": "Request password reset email", "tags": ["Authentication"], "operationId": "post_auth_forgot-password", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/auth/refresh": {"post": {"summary": "Create refresh", "description": "Refresh authentication tokens using refresh token", "tags": ["Authentication"], "operationId": "post_auth_refresh", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/auth/resend-verification": {"post": {"summary": "Create resend-verification", "description": "Resend email verification code", "tags": ["Authentication"], "operationId": "post_auth_resend-verification", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/auth/reset-password": {"post": {"summary": "Create reset-password", "description": "Reset password using reset token", "tags": ["Authentication"], "operationId": "post_auth_reset-password", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/auth/signin": {"post": {"summary": "Create signin", "description": "Authenticate user with email and password", "tags": ["Authentication"], "operationId": "post_auth_signin", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "tokens": {"type": "object", "properties": {"accessToken": {"type": "string"}, "refreshToken": {"type": "string"}, "idToken": {"type": "string"}}}, "user": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "username": {"type": "string"}}}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/auth/signup": {"post": {"summary": "Create signup", "description": "Create a new user account with email and password", "tags": ["Authentication"], "operationId": "post_auth_signup", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/channels": {"get": {"summary": "Get channels", "description": "Get list of channels", "tags": ["Channels"], "operationId": "get_channels", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/channels/{channelId}": {"get": {"summary": "Get channels", "description": "Get channel by ID", "tags": ["Channels"], "operationId": "get_channels", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "channelId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "channelId identifier"}], "security": [{"BearerAuth": []}]}}, "/channels/{channelId}/posts": {"get": {"summary": "Get posts", "description": "Get posts in channel", "tags": ["Channels"], "operationId": "get_channels_posts", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "count": {"type": "integer"}, "hasMore": {"type": "boolean"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "channelId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "channelId identifier"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}, "description": "Number of items to return"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}, "description": "Number of items to skip"}], "security": [{"BearerAuth": []}]}}, "/device-tokens": {"post": {"summary": "Create device-tokens", "description": "Register device token for push notifications", "tags": ["Notifications"], "operationId": "post_device-tokens", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/device-tokens/{deviceId}": {"delete": {"summary": "Delete device-tokens", "description": "Remove device token", "tags": ["Notifications"], "operationId": "delete_device-tokens", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "deviceId identifier"}], "security": [{"BearerAuth": []}]}}, "/health": {"get": {"summary": "Get health", "description": "Health check endpoint to verify API status", "tags": ["System"], "operationId": "get_health", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/kick/auth": {"get": {"summary": "Get auth", "description": "Initiate Kick OAuth flow", "tags": ["Kick Integration"], "operationId": "get_kick_auth", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/kick/callback": {"get": {"summary": "Get callback", "description": "<PERSON><PERSON> <PERSON> callback", "tags": ["Kick Integration"], "operationId": "get_kick_callback", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/kick/link": {"post": {"summary": "Create link", "description": "Link Kick account to user", "tags": ["Kick Integration"], "operationId": "post_kick_link", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/kick/signin": {"post": {"summary": "Create signin", "description": "Sign in with Kick account", "tags": ["Kick Integration"], "operationId": "post_kick_signin", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/media/{mediaId}": {"get": {"summary": "Get media", "description": "Get media by ID", "tags": ["Media"], "operationId": "get_media", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "mediaId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "mediaId identifier"}], "security": [{"BearerAuth": []}]}}, "/media/process": {"post": {"summary": "Create process", "description": "POST /media/process endpoint", "tags": ["Media"], "operationId": "post_media_process", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/media/upload-url": {"post": {"summary": "Create upload-url", "description": "POST /media/upload-url endpoint", "tags": ["Media"], "operationId": "post_media_upload-url", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/notification-preferences": {"get": {"summary": "Get notification-preferences", "description": "Get notification preferences", "tags": ["Notifications"], "operationId": "get_notification-preferences", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}, "put": {"summary": "Update notification-preferences", "description": "Update notification preferences", "tags": ["Notifications"], "operationId": "put_notification-preferences", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/notifications": {"get": {"summary": "Get notifications", "description": "Get user notifications", "tags": ["Notifications"], "operationId": "get_notifications", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/notifications/{notificationId}/read": {"put": {"summary": "Update read", "description": "PUT /notifications/{notificationId}/read endpoint", "tags": ["Notifications"], "operationId": "put_notifications_read", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "notificationId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "notificationId identifier"}], "security": [{"BearerAuth": []}]}}, "/notifications/device-token": {"post": {"summary": "Create device-token", "description": "POST /notifications/device-token endpoint", "tags": ["Notifications"], "operationId": "post_notifications_device-token", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/notifications/preferences": {"put": {"summary": "Update preferences", "description": "PUT /notifications/preferences endpoint", "tags": ["Notifications"], "operationId": "put_notifications_preferences", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/posts": {"get": {"summary": "Get posts", "description": "Get posts feed", "tags": ["Posts"], "operationId": "get_posts", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "count": {"type": "integer"}, "hasMore": {"type": "boolean"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}, "description": "Number of items to return"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}, "description": "Number of items to skip"}], "security": [{"BearerAuth": []}]}, "post": {"summary": "Create posts", "description": "Create a new post", "tags": ["Posts"], "operationId": "post_posts", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/posts/{postId}": {"delete": {"summary": "Delete posts", "description": "Delete post", "tags": ["Posts"], "operationId": "delete_posts", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "postId identifier"}], "security": [{"BearerAuth": []}]}, "get": {"summary": "Get posts", "description": "Get post by ID", "tags": ["Posts"], "operationId": "get_posts", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "count": {"type": "integer"}, "hasMore": {"type": "boolean"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "postId identifier"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}, "description": "Number of items to return"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}, "description": "Number of items to skip"}], "security": [{"BearerAuth": []}]}, "put": {"summary": "Update posts", "description": "Update post", "tags": ["Posts"], "operationId": "put_posts", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "postId identifier"}], "security": [{"BearerAuth": []}]}}, "/posts/{postId}/like": {"delete": {"summary": "Delete like", "description": "Unlike a post", "tags": ["Posts"], "operationId": "delete_posts_like", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "postId identifier"}], "security": [{"BearerAuth": []}]}, "post": {"summary": "Create like", "description": "Like a post", "tags": ["Posts"], "operationId": "post_posts_like", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "postId identifier"}], "security": [{"BearerAuth": []}]}}, "/posts/{postId}/reflexes": {"get": {"summary": "Get reflexes", "description": "Get post reflexes", "tags": ["Posts"], "operationId": "get_posts_reflexes", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "count": {"type": "integer"}, "hasMore": {"type": "boolean"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "postId identifier"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}, "description": "Number of items to return"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}, "description": "Number of items to skip"}], "security": [{"BearerAuth": []}]}, "post": {"summary": "Create reflexes", "description": "Create reflex for post", "tags": ["Posts"], "operationId": "post_posts_reflexes", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "postId identifier"}], "security": [{"BearerAuth": []}]}}, "/posts/followed": {"get": {"summary": "Get followed", "description": "Get posts from followed users", "tags": ["Posts"], "operationId": "get_posts_followed", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "count": {"type": "integer"}, "hasMore": {"type": "boolean"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}, "description": "Number of items to return"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}, "description": "Number of items to skip"}], "security": [{"BearerAuth": []}]}}, "/reflexes/{reflexId}": {"delete": {"summary": "Delete reflexes", "description": "Delete reflex", "tags": ["Reflexes"], "operationId": "delete_reflexes", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "reflexId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "reflexId identifier"}], "security": [{"BearerAuth": []}]}, "put": {"summary": "Update reflexes", "description": "Update reflex", "tags": ["Reflexes"], "operationId": "put_reflexes", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "reflexId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "reflexId identifier"}], "security": [{"BearerAuth": []}]}}, "/twitch/auth": {"get": {"summary": "Get auth", "description": "Initiate Twitch OAuth flow", "tags": ["Twitch Integration"], "operationId": "get_twitch_auth", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/twitch/callback": {"get": {"summary": "Get callback", "description": "<PERSON><PERSON> Twitch OAuth callback", "tags": ["Twitch Integration"], "operationId": "get_twitch_callback", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/twitch/link": {"post": {"summary": "Create link", "description": "Link Twitch account to user", "tags": ["Twitch Integration"], "operationId": "post_twitch_link", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/twitch/signin": {"post": {"summary": "Create signin", "description": "Sign in with Twitch account", "tags": ["Twitch Integration"], "operationId": "post_twitch_signin", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/users/{userId}": {"get": {"summary": "Get users", "description": "Get user profile by ID", "tags": ["Users"], "operationId": "get_users", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "userId identifier"}], "security": [{"BearerAuth": []}]}}, "/users/{userId}/follow": {"delete": {"summary": "Delete follow", "description": "Unfollow a user", "tags": ["Users"], "operationId": "delete_users_follow", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "userId identifier"}], "security": [{"BearerAuth": []}]}, "post": {"summary": "Create follow", "description": "Follow a user", "tags": ["Users"], "operationId": "post_users_follow", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "userId identifier"}], "security": [{"BearerAuth": []}]}}, "/users/me": {"get": {"summary": "Get me", "description": "Get current user profile", "tags": ["Users"], "operationId": "get_users_me", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}, "put": {"summary": "Update me", "description": "Update current user profile", "tags": ["Users"], "operationId": "put_users_me", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/users/username": {"post": {"summary": "Create username", "description": "POST /users/username endpoint", "tags": ["Users"], "operationId": "post_users_username", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/xbox/account": {"delete": {"summary": "Delete account", "description": "Unlink Xbox account", "tags": ["Xbox Integration"], "operationId": "delete_xbox_account", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}, "get": {"summary": "Get account", "description": "Get linked Xbox account info", "tags": ["Xbox Integration"], "operationId": "get_xbox_account", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/xbox/auth": {"get": {"summary": "Get auth", "description": "Initiate Xbox OAuth flow", "tags": ["Xbox Integration"], "operationId": "get_xbox_auth", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/xbox/callback": {"get": {"summary": "Get callback", "description": "<PERSON>le Xbox OAuth callback", "tags": ["Xbox Integration"], "operationId": "get_xbox_callback", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}, "/xbox/create-new-account": {"post": {"summary": "Create create-new-account", "description": "Create new Xbox account link", "tags": ["Xbox Integration"], "operationId": "post_xbox_create-new-account", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/xbox/gameclips": {"get": {"summary": "Get gameclips", "description": "Get Xbox game clips", "tags": ["Xbox Integration"], "operationId": "get_xbox_gameclips", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/xbox/link": {"post": {"summary": "Create link", "description": "Link Xbox account to user", "tags": ["Xbox Integration"], "operationId": "post_xbox_link", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/xbox/relink-account": {"post": {"summary": "Create relink-account", "description": "Relink Xbox account", "tags": ["Xbox Integration"], "operationId": "post_xbox_relink-account", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/xbox/screenshots": {"get": {"summary": "Get screenshots", "description": "Get Xbox screenshots", "tags": ["Xbox Integration"], "operationId": "get_xbox_screenshots", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": [{"BearerAuth": []}]}}, "/xbox/signin": {"post": {"summary": "Create signin", "description": "Sign in with Xbox account", "tags": ["Xbox Integration"], "operationId": "post_xbox_signin", "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "security": []}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "string", "description": "User ID"}, "email": {"type": "string", "format": "email", "description": "User email"}, "username": {"type": "string", "description": "User username"}, "firstName": {"type": "string", "description": "User first name"}, "lastName": {"type": "string", "description": "User last name"}, "displayName": {"type": "string", "description": "User display name"}, "avatarUrl": {"type": "string", "format": "uri", "description": "User avatar URL"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "email", "createdAt", "updatedAt"]}, "Post": {"type": "object", "properties": {"id": {"type": "string", "description": "Post ID"}, "title": {"type": "string", "description": "Post title"}, "content": {"type": "string", "description": "Post content"}, "authorId": {"type": "string", "description": "Author user ID"}, "userId": {"type": "string", "description": "User ID (same as authorId)"}, "mediaId": {"type": "string", "description": "Associated media ID"}, "channelId": {"type": "string", "description": "Channel ID if posted in channel"}, "likes": {"type": "integer", "description": "Number of likes"}, "comments": {"type": "integer", "description": "Number of comments"}, "reflexes": {"type": "integer", "description": "Number of reflexes"}, "status": {"type": "string", "enum": ["draft", "uploading_media", "published"], "description": "Post status"}, "active": {"type": "boolean", "description": "Whether post is active"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "keywords": {"type": "array", "items": {"type": "string"}, "description": "AI-extracted keywords"}, "isLikedByCurrentUser": {"type": "boolean", "description": "Whether current user liked this post"}, "reactions": {"type": "object", "description": "Emoji reactions count"}, "currentUserReactions": {"type": "array", "items": {"type": "string"}, "description": "Current user reactions"}}, "required": ["id", "content", "authorId", "userId", "status", "active", "createdAt", "updatedAt"]}, "Channel": {"type": "object", "properties": {"id": {"type": "string", "description": "Channel ID"}, "name": {"type": "string", "description": "Channel name"}, "description": {"type": "string", "description": "Channel description"}, "isPublic": {"type": "boolean", "description": "Whether channel is public"}, "ownerId": {"type": "string", "description": "Channel owner user ID"}, "memberCount": {"type": "integer", "description": "Number of members"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "name", "isPublic", "ownerId", "createdAt", "updatedAt"]}, "Media": {"type": "object", "properties": {"id": {"type": "string", "description": "Media ID"}, "url": {"type": "string", "format": "uri", "description": "Media URL"}, "mediaType": {"type": "string", "enum": ["image", "video"], "description": "Media type"}, "status": {"type": "string", "enum": ["uploading", "processing", "ready", "failed"], "description": "Media status"}, "userId": {"type": "string", "description": "Owner user ID"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "mediaType", "status", "userId", "createdAt", "updatedAt"]}, "Comment": {"type": "object", "properties": {"id": {"type": "string", "description": "Comment ID"}, "postId": {"type": "string", "description": "Post ID"}, "userId": {"type": "string", "description": "Commenter user ID"}, "content": {"type": "string", "description": "Comment content"}, "likeCount": {"type": "integer", "description": "Number of likes"}, "isActive": {"type": "boolean", "description": "Whether comment is active"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "postId", "userId", "content", "isActive", "createdAt", "updatedAt"]}, "AuthTokens": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "JWT access token"}, "refreshToken": {"type": "string", "description": "JWT refresh token"}, "idToken": {"type": "string", "description": "JWT ID token"}}, "required": ["accessToken", "refreshToken", "idToken"]}, "Error": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "details": {"type": "string", "description": "Error details"}}, "required": ["error"]}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"BearerAuth": []}]}