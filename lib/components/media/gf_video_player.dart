import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../../theme/app_theme.dart';
import '../core/gf_loading.dart';
import '../../utils/app_logger.dart';
import '../../services/media_preloader.dart';

// Global video lifecycle manager to handle app state and visibility
class VideoLifecycleManager {
  static final VideoLifecycleManager _instance =
      VideoLifecycleManager._internal();
  factory VideoLifecycleManager() => _instance;
  VideoLifecycleManager._internal();

  final Set<dynamic> _activeVideoPlayers = {};
  bool _isAppInBackground = false;
  bool _isTabSwitched = false;

  bool get isAppInBackground => _isAppInBackground;

  void registerVideoPlayer(dynamic player) {
    _activeVideoPlayers.add(player);
    AppLogger.debug(
      'VideoLifecycleManager: Registered video player, total: ${_activeVideoPlayers.length}',
    );
  }

  void unregisterVideoPlayer(dynamic player) {
    _activeVideoPlayers.remove(player);
    AppLogger.debug(
      'VideoLifecycleManager: Unregistered video player, total: ${_activeVideoPlayers.length}',
    );
  }

  void onAppBackground() {
    _isAppInBackground = true;
    AppLogger.debug(
      'VideoLifecycleManager: App went to background, pausing all videos',
    );
    for (final player in _activeVideoPlayers) {
      if (player is _GFVideoPlayerState) {
        player._pauseForLifecycle();
      }
    }
  }

  void onAppForeground() {
    _isAppInBackground = false;
    AppLogger.debug('VideoLifecycleManager: App came to foreground');
    // Don't auto-resume videos - let visibility control handle this
  }

  void onTabSwitch(bool switched) {
    if (switched) {
      AppLogger.debug(
        'VideoLifecycleManager: Tab switched, pausing all videos',
      );
      for (final player in _activeVideoPlayers) {
        if (player is _GFVideoPlayerState) {
          player._pauseForLifecycle();
        }
      }
    }
    // Don't keep _isTabSwitched true permanently - only pause momentarily
    _isTabSwitched = false;
  }

  void onReflexDrawerOpen(bool opened) {
    if (opened) {
      AppLogger.debug(
        'VideoLifecycleManager: Reflex drawer opened, pausing all videos',
      );
      for (final player in _activeVideoPlayers) {
        if (player is _GFVideoPlayerState) {
          player._pauseForLifecycle();
        }
      }
    }
    // Don't permanently block videos when drawer is closed
  }

  void onNavigationAway() {
    AppLogger.debug(
      'VideoLifecycleManager: Navigation away detected, pausing all videos',
    );
    for (final player in _activeVideoPlayers) {
      if (player is _GFVideoPlayerState) {
        player._pauseForLifecycle();
      }
    }
  }

  bool get shouldPauseForLifecycle => _isAppInBackground || _isTabSwitched;
}

// Global video controller cache to prevent reloading
class _VideoControllerCache {
  static final Map<String, VideoPlayerController> _controllers = {};
  static final Map<String, int> _usageCount = {};

  static VideoPlayerController? getController(String url) {
    final controller = _controllers[url];
    if (controller != null) {
      try {
        // Try to access the controller to see if it's disposed
        final _ = controller.value;
        return controller;
      } catch (e) {
        // Controller is disposed, remove it from cache
        AppLogger.debug(
          'VideoControllerCache: Removing disposed controller for $url: $e',
        );
        _controllers.remove(url);
        _usageCount.remove(url);
      }
    }
    return null;
  }

  static void setController(String url, VideoPlayerController controller) {
    _controllers[url] = controller;
    _usageCount[url] = (_usageCount[url] ?? 0) + 1;
  }

  static void releaseController(String url) {
    final count = _usageCount[url] ?? 0;
    if (count <= 1) {
      // Last reference, can dispose
      final controller = _controllers.remove(url);
      _usageCount.remove(url);
      controller?.dispose();
      AppLogger.debug('VideoControllerCache: Disposed controller for $url');
    } else {
      _usageCount[url] = count - 1;
      AppLogger.debug(
        'VideoControllerCache: Reduced usage count for $url to ${count - 1}',
      );
    }
  }
}

/// GameFlex video player component
///
/// A reusable video player component that provides consistent video playback
/// functionality across the app with GameFlex styling.
///
/// Example usage:
/// ```dart
/// GFVideoPlayer(
///   videoUrl: post.videoUrl,
///   aspectRatio: 16/9,
///   autoPlay: true,
///   showControls: true,
/// )
/// ```
class GFVideoPlayer extends StatefulWidget {
  final String? videoUrl;
  final double? aspectRatio;
  final bool autoPlay;
  final bool loop;
  final bool muted;
  final bool showControls;
  final bool showMuteButton;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;

  const GFVideoPlayer({
    super.key,
    this.videoUrl,
    this.aspectRatio,
    this.autoPlay = false,
    this.loop = true,
    this.muted = false,
    this.showControls = true,
    this.showMuteButton = true,
    this.onTap,
    this.onLongPress,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
  });

  @override
  State<GFVideoPlayer> createState() => _GFVideoPlayerState();
}

class _GFVideoPlayerState extends State<GFVideoPlayer>
    with WidgetsBindingObserver {
  VideoPlayerController? _controller;
  bool _isPlaying = false;
  bool _isMuted = true;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _isPlaying = widget.autoPlay;
    _isMuted = widget.muted;

    // Register with lifecycle manager and app lifecycle observer
    VideoLifecycleManager().registerVideoPlayer(this);
    WidgetsBinding.instance.addObserver(this);

    _initializeVideo();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        VideoLifecycleManager().onAppBackground();
        break;
      case AppLifecycleState.resumed:
        VideoLifecycleManager().onAppForeground();
        break;
      case AppLifecycleState.hidden:
        // Handle hidden state if needed
        break;
    }
  }

  bool _isControllerDisposed() {
    if (_controller == null) return true;
    try {
      // Try to access the controller value to check if it's disposed
      final _ = _controller!.value;
      return false;
    } catch (e) {
      AppLogger.debug('GFVideoPlayer: Controller is disposed: $e');
      return true;
    }
  }

  void _ensureControllerValid() {
    if (_isControllerDisposed()) {
      AppLogger.debug('GFVideoPlayer: Controller disposed, reinitializing');
      _controller = null;
      setState(() {
        _isLoading = true;
        _hasError = false;
        _isPlaying = false;
      });
      _initializeVideo();
    }
  }

  void _pauseForLifecycle() {
    if (_controller != null && _isPlaying) {
      try {
        if (_controller!.value.isInitialized) {
          _controller!.pause();
          setState(() {
            _isPlaying = false;
          });
          AppLogger.debug('GFVideoPlayer: Paused video for lifecycle event');
        }
      } catch (e) {
        AppLogger.debug(
          'GFVideoPlayer: Failed to pause disposed controller: $e',
        );
        // Controller is disposed, just update the state
        setState(() {
          _isPlaying = false;
        });
      }
    }
  }

  @override
  void didUpdateWidget(GFVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);

    AppLogger.debug(
      'GFVideoPlayer: didUpdateWidget called - oldURL: ${oldWidget.videoUrl}, newURL: ${widget.videoUrl}',
    );

    // Only reinitialize if the video URL actually changed
    if (oldWidget.videoUrl != widget.videoUrl) {
      AppLogger.debug('GFVideoPlayer: Video URL changed, reinitializing');
      if (_controller != null && oldWidget.videoUrl != null) {
        _VideoControllerCache.releaseController(oldWidget.videoUrl!);
      }
      _controller = null;
      _initializeVideo();
    } else {
      AppLogger.debug(
        'GFVideoPlayer: Widget updated but video URL unchanged, keeping existing player',
      );

      // Ensure controller is valid before making changes
      if (!_isControllerDisposed()) {
        // Handle visibility changes for autoPlay
        if (oldWidget.autoPlay != widget.autoPlay && _controller != null) {
          try {
            if (_controller!.value.isInitialized) {
              if (widget.autoPlay &&
                  !_isPlaying &&
                  !VideoLifecycleManager().isAppInBackground) {
                // Should start playing
                AppLogger.debug(
                  'GFVideoPlayer: Starting play due to visibility change ${widget.videoUrl}',
                );
                _controller!.play();
                setState(() {
                  _isPlaying = true;
                });
              } else if (!widget.autoPlay && _isPlaying) {
                // Should stop playing due to visibility
                AppLogger.debug(
                  'GFVideoPlayer: Pausing due to visibility change ${widget.videoUrl}',
                );
                _controller!.pause();
                setState(() {
                  _isPlaying = false;
                });
              }
            }
          } catch (e) {
            AppLogger.debug('GFVideoPlayer: Error during autoplay change: $e');
          }
        }

        // Handle mute changes
        if (oldWidget.muted != widget.muted && _controller != null) {
          try {
            _isMuted = widget.muted;
            _controller!.setVolume(_isMuted ? 0.0 : 1.0);
          } catch (e) {
            AppLogger.debug('GFVideoPlayer: Error during mute change: $e');
          }
        }
      } else {
        AppLogger.debug(
          'GFVideoPlayer: Controller disposed during widget update, will reinitialize on next build',
        );
      }
    }
  }

  @override
  void dispose() {
    // Unregister from lifecycle manager and app lifecycle observer
    VideoLifecycleManager().unregisterVideoPlayer(this);
    WidgetsBinding.instance.removeObserver(this);

    // Release controller from cache
    if (_controller != null && widget.videoUrl != null) {
      _VideoControllerCache.releaseController(widget.videoUrl!);
    }

    super.dispose();
  }

  void _initializeVideo() async {
    if (widget.videoUrl == null || widget.videoUrl!.isEmpty) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    // Prevent multiple simultaneous initializations
    if (_controller != null) {
      try {
        if (_controller!.value.isInitialized) {
          AppLogger.debug(
            'GFVideoPlayer: Controller already initialized, skipping',
          );
          return;
        }
      } catch (e) {
        AppLogger.debug(
          'GFVideoPlayer: Controller disposed during initialization check: $e',
        );
        // Controller is disposed, continue with initialization
        _controller = null;
      }
    }

    try {
      AppLogger.debug('GFVideoPlayer: Initializing video ${widget.videoUrl}');

      // Check if we have a cached controller first
      final cachedController = _VideoControllerCache.getController(
        widget.videoUrl!,
      );
      if (cachedController != null) {
        try {
          if (cachedController.value.isInitialized) {
            AppLogger.debug(
              'GFVideoPlayer: Using cached controller - video should not reload!',
            );
            _controller = cachedController;
            setState(() {
              _isLoading = false;
              _hasError = false;
              _isPlaying = widget.autoPlay;
            });

            // Apply initial settings
            await _controller!.setVolume(_isMuted ? 0.0 : 1.0);
            if (widget.autoPlay &&
                !_controller!.value.isPlaying &&
                !VideoLifecycleManager().isAppInBackground) {
              AppLogger.debug(
                'GFVideoPlayer: Starting autoplay for cached controller ${widget.videoUrl}',
              );
              await _controller!.play();
            }
            return;
          }
        } catch (e) {
          AppLogger.debug(
            'GFVideoPlayer: Cached controller is disposed, creating new one: $e',
          );
          // Controller is disposed, continue to create a new one
        }
      }

      // Check if we have a preloaded controller
      final preloadedController = MediaPreloader.instance
          .getPreloadedVideoController(widget.videoUrl!);

      if (preloadedController != null) {
        AppLogger.debug('GFVideoPlayer: Using preloaded controller');
        _controller = preloadedController;
      } else {
        AppLogger.debug('GFVideoPlayer: Creating new controller');
        // Check if it's a network URL or local file
        if (widget.videoUrl!.startsWith('http')) {
          _controller = VideoPlayerController.networkUrl(
            Uri.parse(widget.videoUrl!),
            videoPlayerOptions: VideoPlayerOptions(
              mixWithOthers: true,
              allowBackgroundPlayback: false,
            ),
          );
        } else {
          _controller = VideoPlayerController.asset(widget.videoUrl!);
        }

        await _controller!.initialize();

        // Cache the newly created controller
        _VideoControllerCache.setController(widget.videoUrl!, _controller!);
      }

      if (mounted) {
        await _controller!.setLooping(widget.loop);

        final initialVolume = _isMuted ? 0.0 : 1.0;
        AppLogger.debug(
          'GFVideoPlayer: Setting initial volume to $initialVolume (muted: $_isMuted)',
        );
        await _controller!.setVolume(initialVolume);

        if (widget.autoPlay) {
          // Only check app background state, not tab switching for initial play
          if (!VideoLifecycleManager().isAppInBackground) {
            AppLogger.debug(
              'GFVideoPlayer: Starting autoplay for ${widget.videoUrl}',
            );
            await _controller!.play();
            _isPlaying = true;
          } else {
            AppLogger.debug(
              'GFVideoPlayer: Skipping autoplay due to app in background',
            );
          }
        }

        setState(() {
          _isLoading = false;
        });

        AppLogger.debug('GFVideoPlayer: Video initialized successfully');
      }
    } catch (e) {
      AppLogger.error('GFVideoPlayer: Failed to initialize video', error: e);
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget videoWidget = _buildVideoContent();

    // Use provided aspect ratio or video's natural aspect ratio
    double? aspectRatio = widget.aspectRatio;
    if (aspectRatio == null && _controller != null) {
      try {
        if (_controller!.value.isInitialized) {
          final size = _controller!.value.size;
          if (size.width > 0 && size.height > 0) {
            aspectRatio = size.width / size.height;
          }
        }
      } catch (e) {
        AppLogger.debug(
          'GFVideoPlayer: Controller disposed in build method: $e',
        );
        // Controller is disposed, will be handled by _buildVideoContent
      }
    }

    if (aspectRatio != null) {
      videoWidget = AspectRatio(aspectRatio: aspectRatio, child: videoWidget);
    }

    return ClipRRect(
      borderRadius: widget.borderRadius ?? BorderRadius.zero,
      child: videoWidget,
    );
  }

  Widget _buildVideoContent() {
    if (widget.videoUrl == null || widget.videoUrl!.isEmpty) {
      return _buildPlaceholder();
    }

    if (_hasError) {
      return _buildErrorState();
    }

    if (_isLoading) {
      return _buildLoadingState();
    }

    // Check if controller is disposed and reinitialize if needed
    _ensureControllerValid();

    if (_controller == null || _isLoading) {
      return _buildLoadingState();
    }

    try {
      if (!_controller!.value.isInitialized) {
        return _buildLoadingState();
      }
    } catch (e) {
      AppLogger.debug(
        'GFVideoPlayer: Controller disposed in _buildVideoContent: $e',
      );
      // Controller is disposed, reinitialize
      _controller = null;
      _initializeVideo();
      return _buildLoadingState();
    }

    return GestureDetector(
      onTap: _handleTap,
      onLongPress: widget.onLongPress,
      child: Stack(
        children: [
          // Actual video player - use FittedBox to prevent stretching
          Center(
            child: FittedBox(
              fit: BoxFit.contain,
              child: Builder(
                builder: (context) {
                  try {
                    final size = _controller!.value.size;
                    return SizedBox(
                      width: size.width,
                      height: size.height,
                      child: VideoPlayer(_controller!),
                    );
                  } catch (e) {
                    AppLogger.debug(
                      'GFVideoPlayer: Controller disposed while building video widget: $e',
                    );
                    // Return a placeholder if controller is disposed
                    return const SizedBox(
                      width: 100,
                      height: 100,
                      child: Center(child: CircularProgressIndicator()),
                    );
                  }
                },
              ),
            ),
          ),

          // Mute button (always visible when enabled)
          if (widget.showMuteButton)
            Positioned(top: 16, right: 16, child: _buildMuteButton()),
        ],
      ),
    );
  }

  Widget _buildPlaceholder() {
    if (widget.placeholder != null) return widget.placeholder!;

    return Container(
      color: AppColors.gfDarkBackground,
      child: const Center(
        child: Icon(
          Icons.videocam_outlined,
          color: AppColors.gfGrayText,
          size: 48,
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      color: Colors.black,
      child: const Center(child: GFLoadingIndicator(color: Colors.white)),
    );
  }

  Widget _buildErrorState() {
    if (widget.errorWidget != null) return widget.errorWidget!;

    return Container(
      color: AppColors.gfDarkBackground,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: AppColors.gfGrayText, size: 48),
            SizedBox(height: 8),
            Text(
              'Failed to load video',
              style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMuteButton() {
    return GestureDetector(
      onTap: () {
        AppLogger.debug('GFVideoPlayer: Mute button tapped');
        _toggleMute();
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        // Larger touch area for better responsiveness
        padding: const EdgeInsets.all(12),
        child: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 128), // 0.5 opacity
            shape: BoxShape.circle,
          ),
          child: Icon(
            _isMuted ? Icons.volume_off : Icons.volume_up,
            color: Colors.white,
            size: 18,
          ),
        ),
      ),
    );
  }

  void _handleTap() {
    if (widget.onTap != null) {
      widget.onTap!();
    } else {
      // Default behavior: toggle play/pause
      _togglePlayPause();
    }
  }

  void _togglePlayPause() async {
    if (_controller == null) return;

    try {
      if (_isPlaying) {
        await _controller!.pause();
      } else {
        await _controller!.play();
      }

      setState(() {
        _isPlaying = !_isPlaying;
      });
    } catch (e) {
      AppLogger.error('GFVideoPlayer: Failed to toggle play/pause', error: e);
    }
  }

  void _toggleMute() async {
    if (_controller == null) {
      AppLogger.debug('GFVideoPlayer: Cannot toggle mute - controller is null');
      return;
    }

    try {
      final newVolume = _isMuted ? 1.0 : 0.0;
      AppLogger.debug(
        'GFVideoPlayer: Setting volume to $newVolume (was muted: $_isMuted)',
      );
      await _controller!.setVolume(newVolume);

      setState(() {
        _isMuted = !_isMuted;
      });

      AppLogger.debug(
        'GFVideoPlayer: Mute toggled successfully (now muted: $_isMuted)',
      );
    } catch (e) {
      AppLogger.error('GFVideoPlayer: Failed to toggle mute', error: e);
    }
  }
}

/// Simplified video player for thumbnails
class GFVideoThumbnail extends StatelessWidget {
  final String? videoUrl;
  final String? thumbnailUrl;
  final double? width;
  final double? height;
  final double? aspectRatio;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;
  final bool showPlayIcon;

  const GFVideoThumbnail({
    super.key,
    this.videoUrl,
    this.thumbnailUrl,
    this.width,
    this.height,
    this.aspectRatio,
    this.onTap,
    this.borderRadius,
    this.showPlayIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    Widget thumbnail = Stack(
      fit: StackFit.expand,
      children: [
        // Thumbnail image
        Container(
          color: AppColors.gfDarkBackground,
          child:
              thumbnailUrl != null && thumbnailUrl!.isNotEmpty
                  ? Image.network(
                    thumbnailUrl!,
                    fit: BoxFit.cover,
                    errorBuilder:
                        (context, error, stackTrace) => _buildPlaceholder(),
                  )
                  : _buildPlaceholder(),
        ),

        // Play icon overlay
        if (showPlayIcon)
          Center(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 128), // 0.5 opacity
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 32,
              ),
            ),
          ),
      ],
    );

    if (aspectRatio != null) {
      thumbnail = AspectRatio(aspectRatio: aspectRatio!, child: thumbnail);
    }

    if (width != null || height != null) {
      thumbnail = SizedBox(width: width, height: height, child: thumbnail);
    }

    if (onTap != null) {
      thumbnail = GestureDetector(onTap: onTap, child: thumbnail);
    }

    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: thumbnail,
    );
  }

  Widget _buildPlaceholder() {
    return const Center(
      child: Icon(
        Icons.videocam_outlined,
        color: AppColors.gfGrayText,
        size: 48,
      ),
    );
  }
}
