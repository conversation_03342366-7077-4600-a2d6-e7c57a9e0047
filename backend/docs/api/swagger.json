{"swagger": "2.0", "info": {"title": "GameFlex API", "version": "0.0.2", "description": "Comprehensive API documentation for the GameFlex platform", "contact": {"name": "GameFlex API Team", "email": "<EMAIL>"}}, "host": "api.gameflex.com", "basePath": "/v1", "schemes": ["https"], "paths": {"/auth/confirm-signup": {"post": {"summary": "Create confirm-signup", "description": "Confirm user signup with verification code", "tags": ["Authentication"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/auth/forgot-password": {"post": {"summary": "Create forgot-password", "description": "Request password reset email", "tags": ["Authentication"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/auth/refresh": {"post": {"summary": "Create refresh", "description": "Refresh authentication tokens using refresh token", "tags": ["Authentication"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/auth/resend-verification": {"post": {"summary": "Create resend-verification", "description": "Resend email verification code", "tags": ["Authentication"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/auth/reset-password": {"post": {"summary": "Create reset-password", "description": "Reset password using reset token", "tags": ["Authentication"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/auth/signin": {"post": {"summary": "Create signin", "description": "Authenticate user with email and password", "tags": ["Authentication"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "tokens": {"type": "object", "properties": {"accessToken": {"type": "string"}, "refreshToken": {"type": "string"}, "idToken": {"type": "string"}}}, "user": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "username": {"type": "string"}}}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/auth/signup": {"post": {"summary": "Create signup", "description": "Create a new user account with email and password", "tags": ["Authentication"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/channels": {"get": {"summary": "Get channels", "description": "Get list of channels", "tags": ["Channels"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/channels/{channelId}": {"get": {"summary": "Get channels", "description": "Get channel by ID", "tags": ["Channels"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "channelId", "in": "path", "required": true, "type": "string", "description": "channelId identifier"}], "security": [{"BearerAuth": []}]}}, "/channels/{channelId}/posts": {"get": {"summary": "Get posts", "description": "Get posts in channel", "tags": ["Channels"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "count": {"type": "integer"}, "hasMore": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "channelId", "in": "path", "required": true, "type": "string", "description": "channelId identifier"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "description": "Number of items to return"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "description": "Number of items to skip"}], "security": [{"BearerAuth": []}]}}, "/device-tokens": {"post": {"summary": "Create device-tokens", "description": "Register device token for push notifications", "tags": ["Notifications"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/device-tokens/{deviceId}": {"delete": {"summary": "Delete device-tokens", "description": "Remove device token", "tags": ["Notifications"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "deviceId", "in": "path", "required": true, "type": "string", "description": "deviceId identifier"}], "security": [{"BearerAuth": []}]}}, "/health": {"get": {"summary": "Get health", "description": "Health check endpoint to verify API status", "tags": ["System"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/kick/auth": {"get": {"summary": "Get auth", "description": "Initiate Kick OAuth flow", "tags": ["Kick Integration"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/kick/callback": {"get": {"summary": "Get callback", "description": "<PERSON><PERSON> <PERSON> callback", "tags": ["Kick Integration"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/kick/link": {"post": {"summary": "Create link", "description": "Link Kick account to user", "tags": ["Kick Integration"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/kick/signin": {"post": {"summary": "Create signin", "description": "Sign in with Kick account", "tags": ["Kick Integration"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/media/{mediaId}": {"get": {"summary": "Get media", "description": "Get media by ID", "tags": ["Media"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "mediaId", "in": "path", "required": true, "type": "string", "description": "mediaId identifier"}], "security": [{"BearerAuth": []}]}}, "/media/process": {"post": {"summary": "Create process", "description": "POST /media/process endpoint", "tags": ["Media"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/media/upload-url": {"post": {"summary": "Create upload-url", "description": "POST /media/upload-url endpoint", "tags": ["Media"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/notification-preferences": {"get": {"summary": "Get notification-preferences", "description": "Get notification preferences", "tags": ["Notifications"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}, "put": {"summary": "Update notification-preferences", "description": "Update notification preferences", "tags": ["Notifications"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/notifications": {"get": {"summary": "Get notifications", "description": "Get user notifications", "tags": ["Notifications"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/notifications/{notificationId}/read": {"put": {"summary": "Update read", "description": "PUT /notifications/{notificationId}/read endpoint", "tags": ["Notifications"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "notificationId", "in": "path", "required": true, "type": "string", "description": "notificationId identifier"}], "security": [{"BearerAuth": []}]}}, "/notifications/device-token": {"post": {"summary": "Create device-token", "description": "POST /notifications/device-token endpoint", "tags": ["Notifications"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/notifications/preferences": {"put": {"summary": "Update preferences", "description": "PUT /notifications/preferences endpoint", "tags": ["Notifications"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/posts": {"get": {"summary": "Get posts", "description": "Get posts feed", "tags": ["Posts"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "count": {"type": "integer"}, "hasMore": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "limit", "in": "query", "required": false, "type": "integer", "description": "Number of items to return"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "description": "Number of items to skip"}], "security": [{"BearerAuth": []}]}, "post": {"summary": "Create posts", "description": "Create a new post", "tags": ["Posts"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/posts/{postId}": {"delete": {"summary": "Delete posts", "description": "Delete post", "tags": ["Posts"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "type": "string", "description": "postId identifier"}], "security": [{"BearerAuth": []}]}, "get": {"summary": "Get posts", "description": "Get post by ID", "tags": ["Posts"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "count": {"type": "integer"}, "hasMore": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "type": "string", "description": "postId identifier"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "description": "Number of items to return"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "description": "Number of items to skip"}], "security": [{"BearerAuth": []}]}, "put": {"summary": "Update posts", "description": "Update post", "tags": ["Posts"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "type": "string", "description": "postId identifier"}], "security": [{"BearerAuth": []}]}}, "/posts/{postId}/like": {"delete": {"summary": "Delete like", "description": "Unlike a post", "tags": ["Posts"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "type": "string", "description": "postId identifier"}], "security": [{"BearerAuth": []}]}, "post": {"summary": "Create like", "description": "Like a post", "tags": ["Posts"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "type": "string", "description": "postId identifier"}], "security": [{"BearerAuth": []}]}}, "/posts/{postId}/reflexes": {"get": {"summary": "Get reflexes", "description": "Get post reflexes", "tags": ["Posts"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "count": {"type": "integer"}, "hasMore": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "type": "string", "description": "postId identifier"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "description": "Number of items to return"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "description": "Number of items to skip"}], "security": [{"BearerAuth": []}]}, "post": {"summary": "Create reflexes", "description": "Create reflex for post", "tags": ["Posts"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "postId", "in": "path", "required": true, "type": "string", "description": "postId identifier"}], "security": [{"BearerAuth": []}]}}, "/posts/followed": {"get": {"summary": "Get followed", "description": "Get posts from followed users", "tags": ["Posts"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/Post"}}, "count": {"type": "integer"}, "hasMore": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "limit", "in": "query", "required": false, "type": "integer", "description": "Number of items to return"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "description": "Number of items to skip"}], "security": [{"BearerAuth": []}]}}, "/reflexes/{reflexId}": {"delete": {"summary": "Delete reflexes", "description": "Delete reflex", "tags": ["Reflexes"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "reflexId", "in": "path", "required": true, "type": "string", "description": "reflexId identifier"}], "security": [{"BearerAuth": []}]}, "put": {"summary": "Update reflexes", "description": "Update reflex", "tags": ["Reflexes"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "reflexId", "in": "path", "required": true, "type": "string", "description": "reflexId identifier"}], "security": [{"BearerAuth": []}]}}, "/twitch/auth": {"get": {"summary": "Get auth", "description": "Initiate Twitch OAuth flow", "tags": ["Twitch Integration"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/twitch/callback": {"get": {"summary": "Get callback", "description": "<PERSON><PERSON> Twitch OAuth callback", "tags": ["Twitch Integration"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/twitch/link": {"post": {"summary": "Create link", "description": "Link Twitch account to user", "tags": ["Twitch Integration"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/twitch/signin": {"post": {"summary": "Create signin", "description": "Sign in with Twitch account", "tags": ["Twitch Integration"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/users/{userId}": {"get": {"summary": "Get users", "description": "Get user profile by ID", "tags": ["Users"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "userId", "in": "path", "required": true, "type": "string", "description": "userId identifier"}], "security": [{"BearerAuth": []}]}}, "/users/{userId}/follow": {"delete": {"summary": "Delete follow", "description": "Unfollow a user", "tags": ["Users"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "userId", "in": "path", "required": true, "type": "string", "description": "userId identifier"}], "security": [{"BearerAuth": []}]}, "post": {"summary": "Create follow", "description": "Follow a user", "tags": ["Users"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "parameters": [{"name": "userId", "in": "path", "required": true, "type": "string", "description": "userId identifier"}], "security": [{"BearerAuth": []}]}}, "/users/me": {"get": {"summary": "Get me", "description": "Get current user profile", "tags": ["Users"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}, "put": {"summary": "Update me", "description": "Update current user profile", "tags": ["Users"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/users/username": {"post": {"summary": "Create username", "description": "POST /users/username endpoint", "tags": ["Users"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/xbox/account": {"delete": {"summary": "Delete account", "description": "Unlink Xbox account", "tags": ["Xbox Integration"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}, "get": {"summary": "Get account", "description": "Get linked Xbox account info", "tags": ["Xbox Integration"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/xbox/auth": {"get": {"summary": "Get auth", "description": "Initiate Xbox OAuth flow", "tags": ["Xbox Integration"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/xbox/callback": {"get": {"summary": "Get callback", "description": "<PERSON>le Xbox OAuth callback", "tags": ["Xbox Integration"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "/xbox/create-new-account": {"post": {"summary": "Create create-new-account", "description": "Create new Xbox account link", "tags": ["Xbox Integration"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/xbox/gameclips": {"get": {"summary": "Get gameclips", "description": "Get Xbox game clips", "tags": ["Xbox Integration"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/xbox/link": {"post": {"summary": "Create link", "description": "Link Xbox account to user", "tags": ["Xbox Integration"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/xbox/relink-account": {"post": {"summary": "Create relink-account", "description": "Relink Xbox account", "tags": ["Xbox Integration"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/xbox/screenshots": {"get": {"summary": "Get screenshots", "description": "Get Xbox screenshots", "tags": ["Xbox Integration"], "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}, "security": [{"BearerAuth": []}]}}, "/xbox/signin": {"post": {"summary": "Create signin", "description": "Sign in with Xbox account", "tags": ["Xbox Integration"], "produces": ["application/json"], "responses": {"201": {"description": "Successful response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}}, "definitions": {"User": {"type": "object", "properties": {"id": {"type": "string", "description": "User ID"}, "email": {"type": "string", "format": "email", "description": "User email"}, "username": {"type": "string", "description": "User username"}, "firstName": {"type": "string", "description": "User first name"}, "lastName": {"type": "string", "description": "User last name"}, "displayName": {"type": "string", "description": "User display name"}, "avatarUrl": {"type": "string", "format": "uri", "description": "User avatar URL"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "email", "createdAt", "updatedAt"]}, "Post": {"type": "object", "properties": {"id": {"type": "string", "description": "Post ID"}, "title": {"type": "string", "description": "Post title"}, "content": {"type": "string", "description": "Post content"}, "authorId": {"type": "string", "description": "Author user ID"}, "userId": {"type": "string", "description": "User ID (same as authorId)"}, "mediaId": {"type": "string", "description": "Associated media ID"}, "channelId": {"type": "string", "description": "Channel ID if posted in channel"}, "likes": {"type": "integer", "description": "Number of likes"}, "comments": {"type": "integer", "description": "Number of comments"}, "reflexes": {"type": "integer", "description": "Number of reflexes"}, "status": {"type": "string", "enum": ["draft", "uploading_media", "published"], "description": "Post status"}, "active": {"type": "boolean", "description": "Whether post is active"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "keywords": {"type": "array", "items": {"type": "string"}, "description": "AI-extracted keywords"}, "isLikedByCurrentUser": {"type": "boolean", "description": "Whether current user liked this post"}, "reactions": {"type": "object", "description": "Emoji reactions count"}, "currentUserReactions": {"type": "array", "items": {"type": "string"}, "description": "Current user reactions"}}, "required": ["id", "content", "authorId", "userId", "status", "active", "createdAt", "updatedAt"]}, "Channel": {"type": "object", "properties": {"id": {"type": "string", "description": "Channel ID"}, "name": {"type": "string", "description": "Channel name"}, "description": {"type": "string", "description": "Channel description"}, "isPublic": {"type": "boolean", "description": "Whether channel is public"}, "ownerId": {"type": "string", "description": "Channel owner user ID"}, "memberCount": {"type": "integer", "description": "Number of members"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "name", "isPublic", "ownerId", "createdAt", "updatedAt"]}, "Media": {"type": "object", "properties": {"id": {"type": "string", "description": "Media ID"}, "url": {"type": "string", "format": "uri", "description": "Media URL"}, "mediaType": {"type": "string", "enum": ["image", "video"], "description": "Media type"}, "status": {"type": "string", "enum": ["uploading", "processing", "ready", "failed"], "description": "Media status"}, "userId": {"type": "string", "description": "Owner user ID"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "mediaType", "status", "userId", "createdAt", "updatedAt"]}, "Comment": {"type": "object", "properties": {"id": {"type": "string", "description": "Comment ID"}, "postId": {"type": "string", "description": "Post ID"}, "userId": {"type": "string", "description": "Commenter user ID"}, "content": {"type": "string", "description": "Comment content"}, "likeCount": {"type": "integer", "description": "Number of likes"}, "isActive": {"type": "boolean", "description": "Whether comment is active"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "postId", "userId", "content", "isActive", "createdAt", "updatedAt"]}, "AuthTokens": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "JWT access token"}, "refreshToken": {"type": "string", "description": "JWT refresh token"}, "idToken": {"type": "string", "description": "JWT ID token"}}, "required": ["accessToken", "refreshToken", "idToken"]}, "Error": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "details": {"type": "string", "description": "Error details"}}, "required": ["error"]}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}