# Implementation Plan

- [x] 1. Update AWS IAM policies for WebSocket support
  - Add WebSocket API Gateway permissions to environment policies
  - Deploy policy changes using backend setup scripts
  - Verify policy updates are applied correctly
  - _Requirements: 8.1_

- [x] 1.1 Update IAM policies for WebSocket permissions
  - Edit backend/setup/policies/development-policy.json to add execute-api permissions
  - Edit backend/setup/policies/staging-policy.json to add execute-api permissions  
  - Edit backend/setup/policies/production-policy.json to add execute-api permissions
  - Add WebSocket API Gateway resource patterns to existing apigateway permissions
  - _Requirements: 8.1_

- [x] 1.2 Deploy policy changes to AWS
  - Run backend/setup/bootstrap.sh to deploy updated policies
  - Verify policies are applied to environment-specific IAM users
  - Test that new permissions are working correctly
  - _Requirements: 8.1_

- [x] 2. Set up WebSocket infrastructure and connection management
  - Create WebSocket API Gateway configuration in Terraform
  - Implement connection handler Lambda with JWT authentication
  - Create DynamoDB tables for connection tracking
  - Test Lambda build and deploy to development environment
  - _Requirements: 8.1, 8.2, 8.3_

- [x] 2.1 Create WebSocket API Gateway Terraform module
  - Write Terraform configuration for WebSocket API Gateway
  - Configure custom domain and SSL certificate
  - Set up connection, message, and disconnect routes
  - _Requirements: 8.1_

- [x] 2.2 Implement WebSocket connection handler Lambda
  - Create connection-handler.ts with connect/disconnect logic
  - Implement JWT token validation using existing auth patterns
  - Store connection records in DynamoDB with TTL
  - Use console.log/console.error for logging consistent with existing code
  - _Requirements: 8.1, 8.2, 8.3_

- [x] 2.3 Create DynamoDB tables for WebSocket connections
  - Add websocket-connections table to Terraform
  - Add notification-queue table for offline users
  - Configure GSI for user-based connection lookups
  - Set up TTL for automatic cleanup
  - _Requirements: 8.1_

- [x] 2.4 Test and deploy WebSocket infrastructure
  - Build Lambda functions using existing build process
  - Deploy to development environment using included deploy script
  - Verify WebSocket API Gateway is accessible
  - Test connection establishment and JWT validation
  - _Requirements: 8.1, 8.2, 8.3_

- [x] 3. Implement message broadcasting system
  - Create SNS topic and message broadcast Lambda
  - Implement message routing and delivery logic
  - Create helper service for other Lambdas to send messages
  - Test Lambda build and deploy to development environment
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 3.1 Create SNS topic for WebSocket message broadcasting
  - Add SNS topic configuration to Terraform
  - Configure topic permissions for Lambda functions
  - Set up dead letter queue for failed messages
  - _Requirements: 3.1_

- [x] 3.2 Implement message broadcast Lambda
  - Create message-handler.ts to process SNS messages
  - Implement connection lookup and message delivery
  - Handle connection cleanup for failed deliveries
  - Add retry logic with exponential backoff
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 3.3 Create WebSocket publisher service
  - Implement sns-publisher.ts helper service
  - Create methods for sending to individual users and groups
  - Integrate with existing notification preferences
  - Add message formatting and validation
  - _Requirements: 3.1, 3.2, 3.4_

- [x] 3.4 Test and deploy message broadcasting system
  - Build Lambda functions using existing build process
  - Deploy to development environment using included deploy script
  - Test SNS message publishing and delivery
  - Verify message routing to correct connections
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 4. Implement WebSocket message routing
  - Create message router Lambda for incoming client messages
  - Implement ping/pong for connection health monitoring
  - Add message validation and error handling
  - Test Lambda build and deploy to development environment
  - _Requirements: 6.1, 6.2, 6.3_

- [x] 4.1 Create message router Lambda
  - Implement message-router.ts for incoming messages
  - Handle ping/pong messages for connection health
  - Add message validation and routing logic
  - Implement error responses for invalid messages
  - _Requirements: 6.1, 6.2_

- [x] 4.2 Add connection health monitoring
  - Implement ping/pong message handling
  - Update connection last-ping timestamps
  - Add automatic cleanup of stale connections
  - Create health check endpoints
  - _Requirements: 6.1, 6.2, 6.3_

- [x] 4.3 Test and deploy message routing system
  - Build Lambda functions using existing build process
  - Deploy to development environment using included deploy script
  - Test ping/pong functionality
  - Verify connection health monitoring
  - _Requirements: 6.1, 6.2, 6.3_

- [x] 5. Integrate WebSocket system with existing notification service
  - Modify existing notification Lambda to publish to SNS
  - Update notification types to support real-time delivery
  - Ensure backward compatibility with push notifications
  - Test Lambda build and deploy to development environment
  - _Requirements: 1.1, 1.2, 1.3, 3.1_

- [x] 5.1 Update existing notification service
  - Modify notifications/index.ts to publish to WebSocket SNS topic
  - Add real-time delivery alongside existing push notifications
  - Maintain existing notification preferences and rate limiting
  - Ensure no breaking changes to existing API
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 5.2 Add WebSocket notification types
  - Extend existing NotificationType enum for real-time messages
  - Add post_update and comment_update message types
  - Create message formatters for different notification types
  - Update notification preferences to include real-time options
  - _Requirements: 1.2, 2.1, 2.2_

- [x] 5.3 Test and deploy notification integration
  - Build Lambda functions using existing build process
  - Deploy to development environment using included deploy script
  - Test end-to-end notification flow from trigger to WebSocket delivery
  - Verify existing push notification functionality still works
  - _Requirements: 1.1, 1.2, 1.3, 3.1_

- [x] 6. Create Flutter WebSocket service
  - Implement WebSocket connection management
  - Add authentication and reconnection logic
  - Create message parsing and routing
  - Write unit tests with mocked connections
  - _Requirements: 1.1, 6.1, 6.2, 6.3_

- [x] 6.1 Implement WebSocket service class
  - Create websocket_service.dart with connection lifecycle management
  - Implement JWT authentication for WebSocket connections
  - Add exponential backoff for reconnection attempts
  - Handle network state changes and app lifecycle events
  - _Requirements: 6.1, 6.2, 6.3, 8.1_

- [x] 6.2 Add message parsing and routing
  - Implement message parsing for different notification types
  - Create message routing to appropriate handlers
  - Add message validation and error handling
  - Implement ping/pong for connection health
  - _Requirements: 1.1, 1.2, 2.1, 6.1_

- [x] 6.3 Create WebSocket service unit tests
  - Write unit tests with mocked WebSocket connections
  - Test connection lifecycle and reconnection logic
  - Test message parsing and routing functionality
  - Ensure no actual network connections in tests
  - _Requirements: 1.1, 6.1, 6.2_

- [x] 7. Implement real-time notification provider
  - Create notification provider for state management
  - Integrate with existing notification service
  - Add unread count tracking and badge updates
  - Write unit tests with mocked services
  - _Requirements: 1.1, 1.2, 1.3, 7.1, 7.2_

- [x] 7.1 Create real-time notification provider
  - Implement realtime_notification_provider.dart extending ChangeNotifier
  - Listen to WebSocket messages and update notification state
  - Integrate with existing NotificationService for API calls
  - Add unread count tracking and badge management
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 7.2 Add notification state management
  - Implement notification list management with real-time updates
  - Add methods for marking notifications as read
  - Handle notification deduplication and ordering
  - Implement pagination for notification history
  - _Requirements: 7.1, 7.2, 7.3, 7.6_

- [x] 7.3 Create notification provider unit tests
  - Write unit tests with mocked WebSocket and API services
  - Test notification state updates and unread count tracking
  - Test mark as read functionality and badge updates
  - Ensure no actual network connections in tests
  - _Requirements: 1.1, 1.2, 7.1_

- [x] 8. Create notification menu UI component
  - Implement notification dropdown/modal widget
  - Add notification history with pagination
  - Create notification item widgets with actions
  - Write widget tests with mocked data
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7_

- [x] 8.1 Implement notification menu widget
  - Create notification_menu.dart with dropdown/modal functionality
  - Add notification icon with unread badge display
  - Implement tap handling to show/hide notification list
  - Add loading states and empty state handling
  - _Requirements: 7.1, 7.4, 7.7_

- [x] 8.2 Create notification item widgets
  - Implement notification_item.dart for individual notifications
  - Add different layouts for different notification types
  - Implement tap handling for navigation to relevant content
  - Add mark as read functionality with visual feedback
  - _Requirements: 7.2, 7.3, 7.5_

- [x] 8.3 Add notification history pagination
  - Implement infinite scroll for notification history
  - Add pull-to-refresh functionality
  - Handle loading states during pagination
  - Add error handling for failed loads
  - _Requirements: 7.6_

- [x] 8.4 Create notification UI widget tests
  - Write widget tests for notification menu interactions
  - Test notification item display and tap handling
  - Test badge count updates and loading states
  - Use mocked data and providers in tests
  - _Requirements: 7.1, 7.2, 7.3_

- [x] 9. Integrate notification system with main app
  - Add notification menu to app bar
  - Initialize WebSocket service in app startup
  - Connect notification provider to existing auth flow
  - Update app navigation to handle notification taps
  - _Requirements: 1.1, 1.2, 2.1, 2.2, 7.1, 7.3_

- [x] 9.1 Add notification menu to app bar
  - Integrate notification menu widget into main app bar
  - Connect to real-time notification provider
  - Add proper positioning and styling
  - Ensure consistent behavior across different screens
  - _Requirements: 7.1, 7.4_

- [x] 9.2 Initialize WebSocket service in app startup
  - Add WebSocket service initialization to main.dart
  - Connect to existing authentication flow
  - Handle connection establishment after login
  - Add proper cleanup on app termination
  - _Requirements: 1.1, 6.1, 8.1_

- [x] 9.3 Update app navigation for notifications
  - Add navigation handling for notification taps
  - Route to appropriate screens based on notification type
  - Update existing screens to handle notification-based navigation
  - Add deep linking support for notification actions
  - _Requirements: 7.3, 2.1, 2.2_

- [x] 10. Add comprehensive error handling and monitoring
  - Implement error handling for all WebSocket scenarios
  - Add monitoring and alerting for connection issues
  - Create health check endpoints for system monitoring
  - Add performance metrics tracking
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 10.1 Add backend error handling and monitoring
  - Implement comprehensive error handling in all Lambda functions
  - Add CloudWatch metrics for connection counts and message delivery
  - Create health check endpoints for monitoring
  - Add alerting for high error rates and connection failures
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 10.2 Add frontend error handling
  - Implement error handling for all WebSocket connection scenarios
  - Add user-friendly error messages and retry options
  - Handle graceful degradation when WebSocket is unavailable
  - Add connection status indicators for users
  - _Requirements: 6.1, 6.2, 6.3_

- [x] 11. Final testing and deployment
  - Run comprehensive end-to-end tests
  - Deploy complete system to development environment
  - Verify all notification types work correctly
  - Test system under load with multiple concurrent connections
  - _Requirements: All requirements_

- [x] 11.1 Run end-to-end integration tests
  - Test complete notification flow from backend trigger to frontend display
  - Verify WebSocket connection handling across different network conditions
  - Test notification menu functionality with real data
  - Validate system performance with multiple concurrent users
  - _Requirements: All requirements_

- [x] 11.2 Final deployment and verification
  - Build and deploy complete system using included deploy script
  - Verify all components are working in development environment
  - Test notification delivery across different user scenarios
  - Validate system monitoring and alerting functionality
  - _Requirements: All requirements_