import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../components/core/gf_loading.dart';
import '../providers/realtime_notification_provider.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import 'notification_item.dart';

/// Notification menu widget that displays a dropdown/modal with notification history
///
/// This widget provides:
/// - Notification icon with unread badge
/// - Dropdown/modal with notification list
/// - Pagination support for notification history
/// - Loading states and empty state handling
/// - Pull-to-refresh functionality
class NotificationMenu extends StatefulWidget {
  /// Callback when a notification is tapped
  final Function(RealtimeNotificationModel)? onNotificationTap;

  /// Custom notification icon (defaults to bell icon)
  final IconData? icon;

  /// Icon size
  final double iconSize;

  /// Badge color for unread count
  final Color? badgeColor;

  /// Badge text color
  final Color? badgeTextColor;

  /// Maximum width for the dropdown menu
  final double maxWidth;

  /// Maximum height for the dropdown menu
  final double maxHeight;

  const NotificationMenu({
    super.key,
    this.onNotificationTap,
    this.icon,
    this.iconSize = 24,
    this.badgeColor,
    this.badgeTextColor,
    this.maxWidth = 400,
    this.maxHeight = 500,
  });

  @override
  State<NotificationMenu> createState() => _NotificationMenuState();
}

class _NotificationMenuState extends State<NotificationMenu> {
  final GlobalKey _buttonKey = GlobalKey();
  OverlayEntry? _overlayEntry;
  bool _isMenuOpen = false;
  final ScrollController _scrollController = ScrollController();
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _closeMenu();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onScroll() {
    // Debounce scroll events to avoid excessive API calls
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 200), () {
      if (_scrollController.hasClients) {
        final position = _scrollController.position;
        // Load more when user scrolls to within 100px of the bottom
        if (position.pixels >= position.maxScrollExtent - 100) {
          _loadMoreNotifications();
        }
      }
    });
  }

  Future<void> _loadMoreNotifications() async {
    final provider = context.read<RealtimeNotificationProvider>();
    if (!provider.isLoading && provider.hasMoreNotifications) {
      try {
        AppLogger.debug('NotificationMenu: Loading more notifications');
        await provider.loadMoreNotifications();
      } catch (e) {
        AppLogger.error(
          'NotificationMenu: Error loading more notifications: $e',
        );
        if (mounted) {
          _showErrorSnackBar('Failed to load more notifications');
        }
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: _loadMoreNotifications,
        ),
      ),
    );
  }

  void _toggleMenu() {
    // Debounce rapid clicks to prevent issues
    if (_debounceTimer?.isActive ?? false) {
      return;
    }

    _debounceTimer = Timer(const Duration(milliseconds: 200), () {});

    if (_isMenuOpen) {
      _closeMenu();
    } else {
      _openMenu();
    }
  }

  void _openMenu() {
    if (_isMenuOpen) return;

    try {
      // Ensure we have a valid context and render box
      final currentContext = _buttonKey.currentContext;
      if (currentContext == null || !mounted) {
        AppLogger.warning(
          'NotificationMenu: Cannot open menu - invalid context',
        );
        return;
      }

      final RenderBox? renderBox =
          currentContext.findRenderObject() as RenderBox?;
      if (renderBox == null) {
        AppLogger.warning('NotificationMenu: Cannot open menu - no render box');
        return;
      }

      final size = renderBox.size;
      final offset = renderBox.localToGlobal(Offset.zero);

      _overlayEntry = _createOverlayEntry(offset, size);
      Overlay.of(context).insert(_overlayEntry!);

      // Only update state if overlay was successfully created
      if (_overlayEntry != null) {
        setState(() {
          _isMenuOpen = true;
        });
      }

      AppLogger.debug('NotificationMenu: Menu opened');
    } catch (e) {
      AppLogger.error('NotificationMenu: Error opening menu: $e');
      // Reset state if opening failed
      _isMenuOpen = false;
      _overlayEntry = null;
    }
  }

  void _closeMenu() {
    if (!_isMenuOpen) return;

    try {
      _overlayEntry?.remove();
      _overlayEntry = null;

      if (mounted) {
        setState(() {
          _isMenuOpen = false;
        });
      } else {
        _isMenuOpen = false;
      }

      AppLogger.debug('NotificationMenu: Menu closed');
    } catch (e) {
      AppLogger.error('NotificationMenu: Error closing menu: $e');
      // Force reset state
      _isMenuOpen = false;
      _overlayEntry = null;
    }
  }

  OverlayEntry _createOverlayEntry(Offset buttonOffset, Size buttonSize) {
    return OverlayEntry(
      builder:
          (context) => _NotificationDropdown(
            buttonOffset: buttonOffset,
            buttonSize: buttonSize,
            maxWidth: widget.maxWidth,
            maxHeight: widget.maxHeight,
            scrollController: _scrollController,
            onNotificationTap: (notification) {
              widget.onNotificationTap?.call(notification);
              _closeMenu();
            },
            onClose: _closeMenu,
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RealtimeNotificationProvider>(
      builder: (context, provider, child) {
        return Stack(
          children: [
            // Notification icon button
            Material(
              color: Colors.transparent,
              child: InkWell(
                key: _buttonKey,
                onTap: _toggleMenu,
                borderRadius: BorderRadius.circular(24),
                splashColor: AppColors.gfGreen.withValues(alpha: 0.2),
                highlightColor: AppColors.gfGreen.withValues(alpha: 0.1),
                child: Container(
                  width: 48,
                  height: 48,
                  alignment: Alignment.center,
                  child: Icon(
                    widget.icon ?? Icons.notifications_outlined,
                    size: widget.iconSize,
                    color:
                        _isMenuOpen ? AppColors.gfGreen : AppColors.gfOffWhite,
                  ),
                ),
              ),
            ),

            // Unread badge
            if (provider.hasUnreadNotifications)
              Positioned(
                right: 8,
                top: 8,
                child: _UnreadBadge(
                  count: provider.unreadCount,
                  color: widget.badgeColor ?? AppColors.gfGreen,
                  textColor: widget.badgeTextColor ?? Colors.black,
                ),
              ),
          ],
        );
      },
    );
  }
}

/// Dropdown menu content for notifications
class _NotificationDropdown extends StatefulWidget {
  final Offset buttonOffset;
  final Size buttonSize;
  final double maxWidth;
  final double maxHeight;
  final ScrollController scrollController;
  final Function(RealtimeNotificationModel) onNotificationTap;
  final VoidCallback onClose;

  const _NotificationDropdown({
    required this.buttonOffset,
    required this.buttonSize,
    required this.maxWidth,
    required this.maxHeight,
    required this.scrollController,
    required this.onNotificationTap,
    required this.onClose,
  });

  @override
  State<_NotificationDropdown> createState() => _NotificationDropdownState();
}

class _NotificationDropdownState extends State<_NotificationDropdown> {
  @override
  void initState() {
    super.initState();
    // Mark notifications as read when menu is opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _markVisibleNotificationsAsRead();
    });
  }

  void _markVisibleNotificationsAsRead() {
    final provider = context.read<RealtimeNotificationProvider>();
    final unreadNotifications = provider.unreadNotifications.take(5).toList();

    for (final notification in unreadNotifications) {
      provider.markAsRead(notification.id).catchError((error) {
        AppLogger.error(
          'NotificationMenu: Error marking notification as read: $error',
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    // Calculate dropdown position
    double left =
        widget.buttonOffset.dx + widget.buttonSize.width - widget.maxWidth;
    double top = widget.buttonOffset.dy + widget.buttonSize.height + 8;

    // Ensure dropdown stays within screen bounds
    if (left < 16) left = 16;
    if (left + widget.maxWidth > screenSize.width - 16) {
      left = screenSize.width - widget.maxWidth - 16;
    }

    if (top + widget.maxHeight > screenSize.height - 16) {
      top = widget.buttonOffset.dy - widget.maxHeight - 8;
    }

    return Stack(
      children: [
        // Backdrop to close menu when tapped
        Positioned.fill(
          child: GestureDetector(
            onTap: widget.onClose,
            child: Container(color: Colors.transparent),
          ),
        ),

        // Dropdown menu
        Positioned(
          left: left,
          top: top,
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(12),
            color: AppColors.gfDarkBackground,
            child: Container(
              width: widget.maxWidth,
              constraints: BoxConstraints(maxHeight: widget.maxHeight),
              decoration: BoxDecoration(
                color: AppColors.gfDarkBackground,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.gfGrayBorder, width: 1),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  _buildHeader(),

                  // Divider
                  Divider(
                    color: AppColors.gfGrayBorder,
                    height: 1,
                    thickness: 1,
                  ),

                  // Notification list
                  Flexible(child: _buildNotificationList()),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Consumer<RealtimeNotificationProvider>(
      builder: (context, provider, child) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Notifications',
                style: TextStyle(
                  color: AppColors.gfOffWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (provider.hasUnreadNotifications)
                TextButton(
                  onPressed: () async {
                    try {
                      await provider.markAllAsRead();
                    } catch (e) {
                      AppLogger.error(
                        'NotificationMenu: Error marking all as read: $e',
                      );
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Failed to mark all as read: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.gfGreen,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                  child: const Text(
                    'Mark all read',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNotificationList() {
    return Consumer<RealtimeNotificationProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading && provider.notifications.isEmpty) {
          return const Padding(
            padding: EdgeInsets.all(32),
            child: Center(child: GFLoadingIndicator(size: 32)),
          );
        }

        if (provider.notifications.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => _handleRefresh(provider),
          color: AppColors.gfGreen,
          backgroundColor: AppColors.gfDarkBackground,
          child: CustomScrollView(
            controller: widget.scrollController,
            slivers: [
              // Notification list
              SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
                  final notification = provider.notifications[index];
                  return NotificationItem(
                    notification: notification,
                    onTap: () => widget.onNotificationTap(notification),
                    onMarkAsRead:
                        () =>
                            _markNotificationAsRead(provider, notification.id),
                    showDivider: index < provider.notifications.length - 1,
                  );
                }, childCount: provider.notifications.length),
              ),

              // Loading indicator for pagination
              if (provider.hasMoreNotifications)
                SliverToBoxAdapter(child: _buildPaginationLoader(provider)),

              // End of list indicator
              if (!provider.hasMoreNotifications &&
                  provider.notifications.isNotEmpty)
                const SliverToBoxAdapter(child: _EndOfListIndicator()),
            ],
          ),
        );
      },
    );
  }

  Future<void> _handleRefresh(RealtimeNotificationProvider provider) async {
    try {
      AppLogger.debug('NotificationMenu: Refreshing notifications');
      await provider.refreshNotifications();
    } catch (e) {
      AppLogger.error('NotificationMenu: Error refreshing notifications: $e');
      if (mounted) {
        widget.onClose();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to refresh notifications'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _markNotificationAsRead(
    RealtimeNotificationProvider provider,
    String notificationId,
  ) async {
    try {
      await provider.markAsRead(notificationId);
    } catch (e) {
      AppLogger.error(
        'NotificationMenu: Error marking notification as read: $e',
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to mark notification as read'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Widget _buildPaginationLoader(RealtimeNotificationProvider provider) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (provider.isLoading) ...[
            const GFLoadingIndicator(size: 24),
            const SizedBox(height: 8),
            const Text(
              'Loading more notifications...',
              style: TextStyle(color: AppColors.gfGrayText, fontSize: 12),
            ),
          ] else ...[
            // Manual load more button as fallback
            TextButton(
              onPressed: () async {
                try {
                  await provider.loadMoreNotifications();
                } catch (e) {
                  AppLogger.error(
                    'NotificationMenu: Error loading more notifications: $e',
                  );
                }
              },
              style: TextButton.styleFrom(foregroundColor: AppColors.gfGreen),
              child: const Text('Load more'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Padding(
      padding: EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.notifications_none, size: 48, color: AppColors.gfGrayText),
          SizedBox(height: 16),
          Text(
            'No notifications yet',
            style: TextStyle(
              color: AppColors.gfOffWhite,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            'You\'ll see notifications here when you have new activity',
            style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// End of list indicator
class _EndOfListIndicator extends StatelessWidget {
  const _EndOfListIndicator();

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          Divider(color: AppColors.gfGrayBorder, thickness: 1),
          SizedBox(height: 8),
          Text(
            'You\'re all caught up!',
            style: TextStyle(
              color: AppColors.gfGrayText,
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Unread notification badge
class _UnreadBadge extends StatelessWidget {
  final int count;
  final Color color;
  final Color textColor;

  const _UnreadBadge({
    required this.count,
    required this.color,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final displayCount = count > 99 ? '99+' : count.toString();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10),
      ),
      constraints: const BoxConstraints(minWidth: 20, minHeight: 20),
      child: Text(
        displayCount,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
