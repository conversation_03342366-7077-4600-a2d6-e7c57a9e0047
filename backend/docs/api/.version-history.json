{"currentVersion": "0.0.2", "versions": [{"version": "0.0.1", "timestamp": "2025-09-18T21:12:48.277Z", "routesHash": "4f53cda18c2baa0c0354bb5f9a3ecbe5ed12ab4d8e11ba873c2f11161202b945", "routeCount": 0}, {"version": "0.0.2", "timestamp": "2025-09-18T21:21:09.838Z", "routesHash": "e30aebb4a180e02803fd485cc338b144e2a447185dc503ed92fcfe58fb350b31", "routeCount": 58}], "changelog": [{"version": "0.0.2", "date": "2025-09-18", "changes": [{"type": "added", "description": "Added 58 new API route(s)"}, {"type": "modified", "description": "Updated Authentication endpoints (7 routes)"}, {"type": "modified", "description": "Updated Channels endpoints (3 routes)"}, {"type": "modified", "description": "Updated Notifications endpoints (8 routes)"}, {"type": "modified", "description": "Updated System endpoints (1 routes)"}, {"type": "modified", "description": "Updated Kick Integration endpoints (4 routes)"}, {"type": "modified", "description": "Updated Media endpoints (3 routes)"}, {"type": "modified", "description": "Updated Posts endpoints (10 routes)"}, {"type": "modified", "description": "Updated Reflexes endpoints (2 routes)"}, {"type": "modified", "description": "Updated Twitch Integration endpoints (4 routes)"}, {"type": "modified", "description": "Updated Users endpoints (6 routes)"}, {"type": "modified", "description": "Updated Xbox Integration endpoints (10 routes)"}]}, {"version": "0.0.1", "date": "2025-09-18", "changes": [{"type": "added", "description": "Initial API documentation with 0 routes"}]}]}