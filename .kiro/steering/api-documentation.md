---
inclusion: always
---

# GameFlex API Documentation Reference

This steering file provides <PERSON><PERSON> with access to the current GameFlex API documentation.

## API Documentation Location

The complete API documentation is automatically generated and maintained at:
- **Kiro API Docs**: #[[file:.kiro/api-docs/api-documentation.json]]
- **Routes Summary**: #[[file:.kiro/api-docs/routes-summary.md]]

## Key Information for Kiro

### Authentication
- Most API endpoints require JWT Bearer token authentication
- Authentication endpoints (`/auth/*`) typically do not require authentication
- Health check endpoint (`/health`) does not require authentication

### Base URL Structure
- Development: `https://dev-api.gameflex.com/v1`
- Staging: `https://staging-api.gameflex.com/v1`
- Production: `https://api.gameflex.com/v1`

### Common Response Patterns
- Success responses typically return 200 (GET/PUT/DELETE) or 201 (POST)
- Error responses include `error` and `details` fields
- Authentication errors return 401
- Validation errors return 400
- Server errors return 500

### Main API Categories
1. **Authentication** (`/auth/*`) - User authentication and token management
2. **Users** (`/users/*`) - User profiles and management
3. **Posts** (`/posts/*`) - Content creation and management
4. **Channels** (`/channels/*`) - Channel management
5. **Media** (`/media/*`) - Media upload and management
6. **Reflexes** (`/reflexes/*`) - Reflex system
7. **Health** (`/health`) - System health checks

### Request/Response Format
- All requests and responses use JSON format
- Content-Type: `application/json`
- CORS headers are included in all responses

## Usage for Code Generation

When generating code that interacts with the GameFlex API:

1. **Always check the current API documentation** in the referenced files above
2. **Use the correct HTTP methods** as specified in the documentation
3. **Include proper authentication headers** for protected endpoints
4. **Handle standard error responses** (400, 401, 500)
5. **Use the correct request/response schemas** as defined in the documentation

## Documentation Updates

The API documentation is automatically updated when:
- New Lambda functions are added
- Existing Lambda functions are modified
- Route patterns change
- API schemas are updated

To manually regenerate documentation, run:
```bash
npm run docs:generate
```

## Version Control

API documentation includes version control with:
- Automatic change detection
- Version incrementing
- Changelog generation
- Historical tracking

Current documentation version and changelog are available in the API documentation files.