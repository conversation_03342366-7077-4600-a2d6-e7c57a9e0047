# Design Document

## Overview

The Socket Notification System provides real-time bidirectional communication between the GameFlex mobile application and AWS backend services. The system leverages AWS API Gateway WebSockets for connection management, SNS for message broadcasting, and DynamoDB for connection tracking and message persistence. The frontend Flutter application maintains persistent WebSocket connections and provides a comprehensive notification UI with history management.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Mobile App (Flutter)"
        A[WebSocket Service] --> B[Notification Provider]
        B --> C[Notification UI]
        C --> D[Notification History]
        E[Auth Service] --> A
    end
    
    subgraph "AWS Infrastructure"
        F[API Gateway WebSocket] --> G[Connection Lambda]
        G --> H[DynamoDB Connections]
        I[SNS Topic] --> J[Message Broadcast Lambda]
        J --> F
        K[Other Lambdas] --> I
        L[DynamoDB Notifications] --> J
    end
    
    A <--> F
    G --> L
    J --> H
```

### Component Interaction Flow

1. **Connection Establishment**: Mobile app authenticates and establishes WebSocket connection
2. **Connection Registration**: Connection Lambda stores connection info in DynamoDB
3. **Message Broadcasting**: Any Lambda publishes to SNS topic with user targeting
4. **Message Delivery**: Broadcast Lambda receives SNS messages and sends to connected clients
5. **Notification Processing**: Mobile app receives messages and updates UI/stores locally

## Components and Interfaces

### AWS Policy Configuration

The WebSocket system requires additional AWS permissions that must be added to the existing IAM policies in `backend/setup/policies/`. The following permissions need to be added to support WebSocket API Gateway and related services:

**Required Permissions**:
- `execute-api:*` for WebSocket API Gateway management
- `apigateway:*` for WebSocket API configuration (already exists)
- `sns:*` for message broadcasting (already exists)
- `dynamodb:*` for connection tracking (already exists)

**Policy Updates Required**:
- Update `development-policy.json`, `staging-policy.json`, and `production-policy.json`
- Add WebSocket API Gateway resource patterns
- Include execute-api permissions for WebSocket connections

**Deployment Process**:
- Use `backend/setup/bootstrap.sh` to deploy policy changes
- Policies are automatically applied to environment-specific IAM users
- Changes take effect immediately for new deployments

### Backend Components

#### 1. WebSocket API Gateway
- **Purpose**: Manages WebSocket connections and routes messages
- **Configuration**: 
  - Custom domain with SSL certificate
  - JWT-based authentication via custom authorizer
  - Connection, message, and disconnect routes

#### 2. Connection Management Lambda
- **File**: `backend/src/websocket/connection-handler.ts`
- **Responsibilities**:
  - Handle WebSocket connect/disconnect events
  - Validate JWT tokens using existing auth service
  - Store/remove connection records in DynamoDB
  - Send connection acknowledgment messages

**Interface**:
```typescript
interface ConnectionEvent {
  requestContext: {
    connectionId: string;
    routeKey: string;
    authorizer?: {
      userId: string;
      username?: string;
    };
  };
}

interface ConnectionRecord {
  connectionId: string;
  userId: string;
  username?: string;
  connectedAt: string;
  lastPingAt: string;
  ttl: number;
}
```

#### 3. Message Broadcast Lambda
- **File**: `backend/src/websocket/message-handler.ts`
- **Responsibilities**:
  - Receive messages from SNS topic
  - Query active connections for target users
  - Send messages to WebSocket connections
  - Handle connection cleanup for failed deliveries

**Interface**:
```typescript
interface BroadcastMessage {
  type: 'notification' | 'post_update' | 'comment_update' | 'system';
  targetUserId?: string;
  targetUserIds?: string[];
  payload: {
    id: string;
    title?: string;
    body?: string;
    data?: Record<string, any>;
    timestamp: string;
  };
}

interface SNSEvent {
  Records: Array<{
    Sns: {
      Message: string; // JSON-encoded BroadcastMessage
      MessageAttributes: Record<string, any>;
    };
  }>;
}
```

#### 4. WebSocket Message Router Lambda
- **File**: `backend/src/websocket/message-router.ts`
- **Responsibilities**:
  - Handle incoming messages from clients
  - Route messages based on type
  - Implement ping/pong for connection health

**Interface**:
```typescript
interface IncomingMessage {
  action: 'ping' | 'subscribe' | 'unsubscribe';
  data?: Record<string, any>;
}

interface OutgoingMessage {
  type: 'pong' | 'notification' | 'error' | 'ack';
  data: Record<string, any>;
  timestamp: string;
}
```

#### 5. SNS Integration Service
- **File**: `backend/src/websocket/sns-publisher.ts`
- **Responsibilities**:
  - Provide helper functions for other Lambdas to publish messages
  - Handle message formatting and targeting
  - Integrate with existing notification preferences

**Interface**:
```typescript
class WebSocketPublisher {
  static async sendToUser(userId: string, message: BroadcastMessage): Promise<void>;
  static async sendToUsers(userIds: string[], message: BroadcastMessage): Promise<void>;
  static async sendNotification(notification: NotificationPayload): Promise<void>;
}
```

#### 6. DynamoDB Tables

**WebSocket Connections Table**:
```
Table: websocket-connections-{environment}
Partition Key: connectionId (String)
Attributes:
- userId (String) - GSI partition key
- username (String)
- connectedAt (String)
- lastPingAt (String)
- ttl (Number) - Auto-cleanup after 24 hours

GSI: userId-index
- Partition Key: userId
- Sort Key: connectedAt
```

**Notification Queue Table** (for offline users):
```
Table: notification-queue-{environment}
Partition Key: userId (String)
Sort Key: timestamp (String)
Attributes:
- notificationId (String)
- type (String)
- payload (Map)
- ttl (Number) - Auto-cleanup after 7 days
```

### Frontend Components

#### 1. WebSocket Service
- **File**: `lib/services/websocket_service.dart`
- **Responsibilities**:
  - Manage WebSocket connection lifecycle
  - Handle authentication and reconnection
  - Parse incoming messages and route to appropriate handlers
  - Implement exponential backoff for reconnection

**Interface**:
```dart
class WebSocketService {
  Stream<WebSocketMessage> get messageStream;
  Future<void> connect();
  Future<void> disconnect();
  Future<void> sendMessage(Map<String, dynamic> message);
  bool get isConnected;
  ConnectionState get connectionState;
}

enum ConnectionState { disconnected, connecting, connected, reconnecting }

class WebSocketMessage {
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
}
```

#### 2. Real-time Notification Provider
- **File**: `lib/providers/realtime_notification_provider.dart`
- **Responsibilities**:
  - Listen to WebSocket messages
  - Update notification state
  - Manage unread counts
  - Integrate with existing notification service

**Interface**:
```dart
class RealtimeNotificationProvider extends ChangeNotifier {
  List<NotificationModel> get notifications;
  int get unreadCount;
  bool get hasUnreadNotifications;
  
  Future<void> markAsRead(String notificationId);
  Future<void> markAllAsRead();
  Future<void> loadMoreNotifications();
}
```

#### 3. Notification Menu Widget
- **File**: `lib/widgets/notification_menu.dart`
- **Responsibilities**:
  - Display notification dropdown/modal
  - Show notification history with pagination
  - Handle notification interactions
  - Display unread badge

**Interface**:
```dart
class NotificationMenu extends StatefulWidget {
  final Widget child; // Notification icon button
  final VoidCallback? onNotificationTap;
}

class NotificationMenuItem extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsRead;
}
```

#### 4. Enhanced Notification Model
- **File**: `lib/models/realtime_notification_model.dart`
- **Extends**: Existing `NotificationModel`
- **Additional Properties**:
  - `isRealtime: bool`
  - `receivedAt: DateTime`
  - `source: NotificationSource` (websocket, push, api)

## Data Models

### WebSocket Message Format
```json
{
  "type": "notification",
  "data": {
    "id": "notification-uuid",
    "userId": "user-uuid",
    "type": "comment",
    "title": "New Comment",
    "body": "Someone commented on your post",
    "actorUserId": "actor-uuid",
    "actorUsername": "username",
    "actorDisplayName": "Display Name",
    "actorAvatarUrl": "https://...",
    "data": {
      "postId": "post-uuid",
      "commentId": "comment-uuid"
    },
    "isRead": false,
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Connection Health Message
```json
{
  "type": "ping",
  "timestamp": "2024-01-01T00:00:00Z"
}

{
  "type": "pong",
  "data": {
    "serverTime": "2024-01-01T00:00:00Z",
    "connectionId": "connection-id"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Error Handling

### Backend Error Handling

1. **Connection Failures**:
   - Use console.error for connection errors with context
   - Clean up orphaned connection records
   - Return appropriate HTTP status codes

2. **Message Delivery Failures**:
   - Retry failed message deliveries (max 3 attempts)
   - Remove stale connections from database
   - Queue critical notifications for offline users
   - Use console.log for delivery status tracking

3. **Authentication Errors**:
   - Reject connections with invalid/expired tokens
   - Use console.error for authentication failures
   - Return clear error messages to clients

### Frontend Error Handling

1. **Connection Errors**:
   - Implement exponential backoff for reconnection
   - Show connection status to users
   - Gracefully degrade to polling when WebSocket fails

2. **Message Processing Errors**:
   - Log malformed messages
   - Continue processing other messages
   - Show error notifications to users when appropriate

3. **Authentication Errors**:
   - Trigger token refresh when connection is rejected
   - Redirect to login if refresh fails
   - Clear stored connection state

## Testing Strategy

### Backend Testing

1. **Unit Tests**:
   - Connection handler logic
   - Message broadcasting logic
   - SNS integration functions
   - Authentication validation

2. **Integration Tests**:
   - WebSocket connection flow
   - Message delivery end-to-end
   - Database operations
   - SNS topic publishing

3. **Load Tests**:
   - Concurrent connection handling
   - Message throughput testing
   - Connection cleanup performance

### Frontend Testing

1. **Unit Tests**:
   - WebSocket service connection logic (with mocked connections)
   - Message parsing and routing
   - Notification provider state management
   - UI component rendering

2. **Widget Tests**:
   - Notification menu interactions
   - Notification item display
   - Badge count updates
   - Loading states

3. **Integration Tests** (with mocked services):
   - Notification flow with mock WebSocket service
   - Connection state management with mock connections
   - Offline/online state transitions
   - Authentication integration with mock auth service

**Note**: All Flutter tests will use mocked services and avoid actual HTTP/WebSocket connections since remote connections are disabled in Flutter test environment.

### Manual Testing Scenarios

1. **Connection Management**:
   - Connect/disconnect cycles
   - Network interruption recovery
   - Multiple device connections
   - Token expiration handling

2. **Message Delivery**:
   - Real-time notification delivery
   - Message ordering and deduplication
   - Offline message queuing
   - Cross-user message targeting

3. **UI/UX Testing**:
   - Notification menu usability
   - Badge count accuracy
   - Message history pagination
   - Performance with large notification lists

## Security Considerations

### Authentication and Authorization
- JWT token validation for WebSocket connections
- User-specific message filtering
- Connection isolation between users
- Token refresh handling for long-lived connections

### IAM Policy Updates
The WebSocket infrastructure requires additional IAM permissions that have been added to the existing policy files in `backend/setup/policies/`:
- WebSocket API Gateway permissions (`arn:aws:apigateway:*::/apis*`)
- SNS topic permissions for message broadcasting
- These updates are included in all environment policies (development, staging, production)

### Data Protection
- Encrypt sensitive data in transit (WSS)
- Sanitize message content before broadcasting
- Implement rate limiting for message sending
- Audit logging for security events

### Connection Security
- Validate connection origins
- Implement connection limits per user
- Monitor for suspicious connection patterns
- Automatic cleanup of stale connections

## Performance Considerations

### Backend Optimization
- Connection pooling for DynamoDB operations
- Batch processing for multiple message deliveries
- Efficient connection lookup using GSI
- TTL-based automatic cleanup of old records

### Frontend Optimization
- Message deduplication to prevent duplicates
- Efficient state updates using ChangeNotifier
- Lazy loading of notification history
- Memory management for large notification lists

### Scalability
- Horizontal scaling of Lambda functions
- DynamoDB auto-scaling configuration
- SNS topic partitioning for high throughput
- Connection load balancing across API Gateway instances

## Monitoring and Observability

### Metrics to Track
- Active WebSocket connections count
- Message delivery success/failure rates
- Connection establishment/termination rates
- Average message delivery latency
- Authentication failure rates

### Logging Strategy
- Use existing console.log/console.error pattern consistent with current codebase
- Connection lifecycle events with user context
- Message delivery attempts and results
- Error conditions with full context
- Performance metrics and timing data
- Structured JSON logging for important events

### Alerting
- High connection failure rates
- Message delivery failures above threshold
- Authentication error spikes
- Database operation failures
- Lambda function errors and timeouts