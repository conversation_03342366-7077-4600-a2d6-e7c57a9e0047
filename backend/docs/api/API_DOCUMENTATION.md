# GameFlex API Documentation

Version: 0.0.2
Generated: 2025-09-18T21:21:09.842Z

## Overview

This document provides comprehensive documentation for the GameFlex API.

## Authentication

### POST /auth/confirm-signup

Confirm user signup with verification code

**Summary:** Create confirm-signup

---

### POST /auth/forgot-password

Request password reset email

**Summary:** Create forgot-password

---

### POST /auth/refresh

Refresh authentication tokens using refresh token

**Summary:** Create refresh

---

### POST /auth/resend-verification

Resend email verification code

**Summary:** Create resend-verification

---

### POST /auth/reset-password

Reset password using reset token

**Summary:** Create reset-password

---

### POST /auth/signin

Authenticate user with email and password

**Summary:** Create signin

---

### POST /auth/signup

Create a new user account with email and password

**Summary:** Create signup

---

## Channels

### GET /channels

Get list of channels

**Summary:** Get channels

**Authentication:** Required

---

### GET /channels/{channelId}

Get channel by ID

**Summary:** Get channels

**Authentication:** Required

**Path Parameters:**

- `channelId` (string, required): channelId identifier

---

### GET /channels/{channelId}/posts

Get posts in channel

**Summary:** Get posts

**Authentication:** Required

**Path Parameters:**

- `channelId` (string, required): channelId identifier

---

## Notifications

### POST /device-tokens

Register device token for push notifications

**Summary:** Create device-tokens

**Authentication:** Required

---

### DELETE /device-tokens/{deviceId}

Remove device token

**Summary:** Delete device-tokens

**Authentication:** Required

**Path Parameters:**

- `deviceId` (string, required): deviceId identifier

---

### GET /notification-preferences

Get notification preferences

**Summary:** Get notification-preferences

**Authentication:** Required

---

### PUT /notification-preferences

Update notification preferences

**Summary:** Update notification-preferences

**Authentication:** Required

---

### GET /notifications

Get user notifications

**Summary:** Get notifications

**Authentication:** Required

---

### PUT /notifications/{notificationId}/read

PUT /notifications/{notificationId}/read endpoint

**Summary:** Update read

**Authentication:** Required

**Path Parameters:**

- `notificationId` (string, required): notificationId identifier

---

### POST /notifications/device-token

POST /notifications/device-token endpoint

**Summary:** Create device-token

**Authentication:** Required

---

### PUT /notifications/preferences

PUT /notifications/preferences endpoint

**Summary:** Update preferences

**Authentication:** Required

---

## System

### GET /health

Health check endpoint to verify API status

**Summary:** Get health

---

## Kick Integration

### GET /kick/auth

Initiate Kick OAuth flow

**Summary:** Get auth

---

### GET /kick/callback

Handle Kick OAuth callback

**Summary:** Get callback

---

### POST /kick/link

Link Kick account to user

**Summary:** Create link

**Authentication:** Required

---

### POST /kick/signin

Sign in with Kick account

**Summary:** Create signin

---

## Media

### GET /media/{mediaId}

Get media by ID

**Summary:** Get media

**Authentication:** Required

**Path Parameters:**

- `mediaId` (string, required): mediaId identifier

---

### POST /media/process

POST /media/process endpoint

**Summary:** Create process

**Authentication:** Required

---

### POST /media/upload-url

POST /media/upload-url endpoint

**Summary:** Create upload-url

**Authentication:** Required

---

## Posts

### GET /posts

Get posts feed

**Summary:** Get posts

**Authentication:** Required

---

### POST /posts

Create a new post

**Summary:** Create posts

**Authentication:** Required

---

### DELETE /posts/{postId}

Delete post

**Summary:** Delete posts

**Authentication:** Required

**Path Parameters:**

- `postId` (string, required): postId identifier

---

### GET /posts/{postId}

Get post by ID

**Summary:** Get posts

**Authentication:** Required

**Path Parameters:**

- `postId` (string, required): postId identifier

---

### PUT /posts/{postId}

Update post

**Summary:** Update posts

**Authentication:** Required

**Path Parameters:**

- `postId` (string, required): postId identifier

---

### DELETE /posts/{postId}/like

Unlike a post

**Summary:** Delete like

**Authentication:** Required

**Path Parameters:**

- `postId` (string, required): postId identifier

---

### POST /posts/{postId}/like

Like a post

**Summary:** Create like

**Authentication:** Required

**Path Parameters:**

- `postId` (string, required): postId identifier

---

### GET /posts/{postId}/reflexes

Get post reflexes

**Summary:** Get reflexes

**Authentication:** Required

**Path Parameters:**

- `postId` (string, required): postId identifier

---

### POST /posts/{postId}/reflexes

Create reflex for post

**Summary:** Create reflexes

**Authentication:** Required

**Path Parameters:**

- `postId` (string, required): postId identifier

---

### GET /posts/followed

Get posts from followed users

**Summary:** Get followed

**Authentication:** Required

---

## Reflexes

### DELETE /reflexes/{reflexId}

Delete reflex

**Summary:** Delete reflexes

**Authentication:** Required

**Path Parameters:**

- `reflexId` (string, required): reflexId identifier

---

### PUT /reflexes/{reflexId}

Update reflex

**Summary:** Update reflexes

**Authentication:** Required

**Path Parameters:**

- `reflexId` (string, required): reflexId identifier

---

## Twitch Integration

### GET /twitch/auth

Initiate Twitch OAuth flow

**Summary:** Get auth

---

### GET /twitch/callback

Handle Twitch OAuth callback

**Summary:** Get callback

---

### POST /twitch/link

Link Twitch account to user

**Summary:** Create link

**Authentication:** Required

---

### POST /twitch/signin

Sign in with Twitch account

**Summary:** Create signin

---

## Users

### GET /users/{userId}

Get user profile by ID

**Summary:** Get users

**Authentication:** Required

**Path Parameters:**

- `userId` (string, required): userId identifier

---

### DELETE /users/{userId}/follow

Unfollow a user

**Summary:** Delete follow

**Authentication:** Required

**Path Parameters:**

- `userId` (string, required): userId identifier

---

### POST /users/{userId}/follow

Follow a user

**Summary:** Create follow

**Authentication:** Required

**Path Parameters:**

- `userId` (string, required): userId identifier

---

### GET /users/me

Get current user profile

**Summary:** Get me

**Authentication:** Required

---

### PUT /users/me

Update current user profile

**Summary:** Update me

**Authentication:** Required

---

### POST /users/username

POST /users/username endpoint

**Summary:** Create username

**Authentication:** Required

---

## Xbox Integration

### DELETE /xbox/account

Unlink Xbox account

**Summary:** Delete account

**Authentication:** Required

---

### GET /xbox/account

Get linked Xbox account info

**Summary:** Get account

**Authentication:** Required

---

### GET /xbox/auth

Initiate Xbox OAuth flow

**Summary:** Get auth

---

### GET /xbox/callback

Handle Xbox OAuth callback

**Summary:** Get callback

---

### POST /xbox/create-new-account

Create new Xbox account link

**Summary:** Create create-new-account

**Authentication:** Required

---

### GET /xbox/gameclips

Get Xbox game clips

**Summary:** Get gameclips

**Authentication:** Required

---

### POST /xbox/link

Link Xbox account to user

**Summary:** Create link

**Authentication:** Required

---

### POST /xbox/relink-account

Relink Xbox account

**Summary:** Create relink-account

**Authentication:** Required

---

### GET /xbox/screenshots

Get Xbox screenshots

**Summary:** Get screenshots

**Authentication:** Required

---

### POST /xbox/signin

Sign in with Xbox account

**Summary:** Create signin

---

